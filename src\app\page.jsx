"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function LandingPage() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const addToRefs = (el) => {
    if (el && !sectionRefs.current.includes(el)) {
      sectionRefs.current.push(el);
    }
  };

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false, // useful for debugging
            },
          }
        );
      });
    });
  }, [isClient]);

  const [formStatus, setFormStatus] = useState("");
  const initialFormData = {
    name: "",
    email: "",
    age: "",
    height: "",
    currentWeight: "",
    targetWeight: "",
    phone: "",
  };
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState(initialFormData);
  const handleSubmit = async (e) => {
    e.preventDefault(); // Prevent the default form submission
    setLoading(true);

    console.log("Payload 1 ==== ", formData);

    // Check if all fields are filled
    const allFieldsFilled = Object.values(formData).every(
      (value) => value.trim() !== ""
    );

    if (!allFieldsFilled) {
      setFormStatus("All fields are required."); // Set message if any field is empty
      setLoading(false);
      return; // Stop submission if validation fails
    }

    // Send data as JSON to the specified URL
    try {
      console.log("Payload ==== ", formData);
      const response = await fetch(
        "https://hook.eu2.make.com/anhpmodb832ttyr0f75g1vjknev7yd97",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        }
      );
      console.log("response === ", response);

      if (response.ok) {
        setFormStatus("Success! Your information has been submitted."); // Success message
        setFormData(initialFormData);
      } else {
        setFormStatus("Oops! Something went wrong. Please try again."); // Failure message
      }
    } catch (error) {
      setFormStatus("Error: Unable to submit form. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-primary text-white theme-secondary-font">
      {/* Hero Section */}
      <section
        className="pt-24 bg-cover bg-center h-screen overflow-auto flex items-center justify-center"
        style={{ backgroundImage: "url('images/hero.png')" }}
      >
        <div className="text-center max-w-4xl" ref={heroRef}>
          <h1 className="text-5xl md:text-8xl font-extrabold uppercase leading-tight theme-primary-font">
            Fuel your body
            <br />
            Elevate your soul
          </h1>
          <p className="mt-4 text-2xl">
            Transform your life with personalized diet charts and workout
            programs crafted for real results — inside and out.
          </p>
          <a
            href="#how-it-works"
            className="mt-6 inline-block bg-teal-500 text-white font-bold py-3 px-6 rounded-full hover:bg-accent-dark transition"
          >
            HOW IT WORKS
          </a>
        </div>
      </section>

      <section
        className="bg-teal-600 py-16 text-center bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div>
          <div className="text-center mb-10">
            <h2
              className="text-4xl md:text-6xl font-bold text-white mb-4 theme-primary-font text-teal-600"
              ref={addToRefs}
            >
              FOOD FOR SOUL
            </h2>
          </div>
          <div className="max-w-6xl mx-auto px-4 grid md:grid-cols-2 gap-8 items-center">
            <div className="grid grid-cols-2 gap-6 text-center">
              <div ref={addToRefs}>
                <p className="text-4xl font-bold text-accent theme-primary-font text-6xl md:text-8xl">
                  1000+
                </p>
                <p className="mt-1 uppercase">Transformations</p>
              </div>
              <div ref={addToRefs}>
                <p className="text-4xl font-bold text-accent theme-primary-font text-6xl md:text-8xl">
                  10+
                </p>
                <p className="mt-1 uppercase">Years of Experience</p>
              </div>
              <div ref={addToRefs}>
                <Image
                  src="/images/icon-1.png"
                  alt="Personalized Program"
                  width={100}
                  height={100}
                  className="mx-auto"
                />
                <p className="mt-3 uppercase">Personalized Program</p>
              </div>
              <div ref={addToRefs}>
                <Image
                  src="/images/icon-1.png"
                  alt="Sustainable Results"
                  width={100}
                  height={100}
                  className="mx-auto"
                />
                <p className="mt-3 uppercase">Sustainable Results</p>
              </div>
            </div>
            <div className="text-center md:text-start" ref={addToRefs}>
              <p className="mb-6 text-2xl">
                Food for Soul is a one-point solution for your needs related to
                a Healthy body. Our body has 7 chakras: Muldhara, Swadisthana,
                Manipura, Anahata, Vishuddha, Ajna & Sahasrsara. Manipura chakra
                or Solar plexus is associated with Fire and Food. By controlling
                what you eat, you can get control over it.
              </p>
              <p className="font-semibold text-2xl">
                What is good for your body, is good for your Soul!
              </p>
            </div>
          </div>
          <a
            ref={addToRefs}
            href="#register"
            className="mt-10 inline-block bg-teal-400 text-white font-bold py-3 px-6 rounded-full hover:bg-accent-dark transition"
          >
            REGISTER NOW
          </a>
        </div>
      </section>

      <section id="how-it-works" className="py-16 bg-teal-100 text-black">
        <div className="max-w-6xl mx-auto px-4 text-center">
          <h2
            className="text-4xl md:text-6xl font-bold text-accent mb-10 theme-primary-font text-teal-600"
            ref={addToRefs}
          >
            HOW IT WORKS
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 text-center">
            <div ref={addToRefs}>
              <Image
                src="/images/icon-6.png"
                alt="Register"
                width={100}
                height={100}
                className="mx-auto"
              />
              <p className="mt-3">Register</p>
            </div>
            <div ref={addToRefs}>
              <Image
                src="/images/icon-5.png"
                alt="Choose Plan"
                width={100}
                height={100}
                className="mx-auto"
              />
              <p className="mt-3">Choose Plan</p>
            </div>
            <div ref={addToRefs}>
              <Image
                src="/images/icon-3.png"
                alt="Connect with Coach"
                width={100}
                height={100}
                className="mx-auto"
              />
              <p className="mt-3">Connect with Coach</p>
            </div>
            <div ref={addToRefs}>
              <Image
                src="/images/icon-2.png"
                alt="Start as Advised"
                width={100}
                height={100}
                className="mx-auto"
              />
              <p className="mt-3">Start as Advised</p>
            </div>
            <div
              ref={addToRefs}
              className="col-span-2 md:col-span-1 lg:col-span-1 mx-auto"
            >
              <Image
                src="/images/icon-4.png"
                alt="Experience Result"
                width={100}
                height={100}
                className="mx-auto"
              />
              <p className="mt-3">Experience Result</p>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-teal-100 text-black">
        <div className="max-w-6xl mx-auto px-4 text-center">
          <h2
            ref={addToRefs}
            className="text-4xl md:text-6xl font-bold text-accent mb-10 theme-primary-font text-teal-600"
          >
            PROGRAMS
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div ref={addToRefs}>
              <Image
                src="/images/food.png"
                alt="Choose Plan"
                width={100}
                height={100}
                className="mx-auto"
              />
              <p className="mt-3 theme-primary-font text-3xl">Diet Plan</p>
              <p className="mt-3">Updated every 15 days.</p>
            </div>
            <div ref={addToRefs}>
              <Image
                src="/images/icon-2.png"
                alt="Connect with Coach"
                width={100}
                height={100}
                className="mx-auto"
              />
              <p className="mt-3 theme-primary-font text-3xl">Workout Plan</p>
              <p className="mt-3">Updated every 15 days.</p>
            </div>
            <div ref={addToRefs}>
              <Image
                src="/images/food-workout.png"
                alt="Start as Advised"
                width={100}
                height={100}
                className="mx-auto"
              />
              <p className="mt-3 theme-primary-font text-3xl">
                Diet + Workout Plan
              </p>
              <p className="mt-3">Updated every 15 days.</p>
            </div>
            <div ref={addToRefs}>
              <Image
                src="/images/premium.png"
                alt="Experience Result"
                width={100}
                height={100}
                className="mx-auto"
              />
              <p className="mt-3 theme-primary-font text-3xl">Premium Plan</p>
              <p className="mt-3">VC included 12 sessions every month</p>
            </div>
          </div>
        </div>
      </section>

      <section
        id="register"
        className="bg-teal-600 py-16 text-center md:h-screen bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: "url('/images/bg.png')" }}
      >
        <div className="max-w-6xl mx-auto px-4 grid md:grid-cols-2 gap-8 items-center">
          <div ref={addToRefs}>
            <Image
              src="/images/transformation-1.png"
              alt="Before and After"
              width={500}
              height={500}
              className="rounded-lg shadow-lg"
            />
          </div>

          <form
            onSubmit={handleSubmit}
            ref={addToRefs}
            className="grid grid-cols-1 gap-4"
          >
            <h2 className="col-span-2 text-4xl md:text-6xl font-bold text-white mb-3 theme-primary-font text-teal-600">
              REGISTER NOW
            </h2>
            <input
              type="text"
              placeholder="Name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              className="input bg-white/20 backdrop-blur-md px-4 py-3 rounded col-span-2 sm:col-span-1"
            />
            <input
              type="email"
              placeholder="Email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              className="input bg-white/20 backdrop-blur-md px-4 py-3 rounded col-span-2 sm:col-span-1"
            />
            <input
              type="number"
              placeholder="Age (in years)"
              value={formData.age}
              onChange={(e) =>
                setFormData({ ...formData, age: e.target.value })
              }
              className="input bg-white/20 backdrop-blur-md px-4 py-3 rounded col-span-2 sm:col-span-1"
            />
            <input
              type="text"
              placeholder="Height (in cm)"
              value={formData.height}
              onChange={(e) =>
                setFormData({ ...formData, height: e.target.value })
              }
              className="input bg-white/20 backdrop-blur-md px-4 py-3 rounded col-span-2 sm:col-span-1"
            />
            <input
              type="text"
              placeholder="Current Weight (in kg)"
              value={formData.currentWeight}
              onChange={(e) =>
                setFormData({ ...formData, currentWeight: e.target.value })
              }
              className="input bg-white/20 backdrop-blur-md px-4 py-3 rounded col-span-2 sm:col-span-1"
            />
            <input
              type="text"
              placeholder="Target Weight (in kg)"
              value={formData.targetWeight}
              onChange={(e) =>
                setFormData({ ...formData, targetWeight: e.target.value })
              }
              className="input bg-white/20 backdrop-blur-md px-4 py-3 rounded col-span-2 sm:col-span-1"
            />
            <input
              type="text"
              placeholder="Phone"
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
              className="input col-span-2 bg-white/20 backdrop-blur-md px-4 py-3 rounded"
            />
            <button
              type="submit"
              className="col-span-2 inline-block bg-teal-400 text-white font-bold py-3 px-6 rounded-full hover:bg-accent-dark transition"
              disabled={loading} // Disable the button when loading
            >
              {loading ? "SUBMITTING..." : "SUBMIT"}
            </button>
            {/* Display success or error message */}
            {formStatus && (
              <p
                className={`mt-4 col-span-2 ${
                  formStatus.includes("Success")
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {formStatus}
              </p>
            )}
          </form>
        </div>
      </section>

      <section className="py-16 bg-teal-100 text-black text-center bg-cover bg-center flex items-center justify-center">
        <div className="max-w-6xl mx-auto px-4 grid md:grid-cols-2 gap-8 items-center">
          <div>
            <h2
              className="text-4xl md:text-6xl font-bold text-accent mb-10 theme-primary-font text-teal-600"
              ref={addToRefs}
            >
              <span className="text-stone-900">Mobile Application</span>
              <br />
              COMING SOON
            </h2>
          </div>

          <div ref={addToRefs}>
            <Image
              src="/images/mobile-application.jpg"
              alt="Before and After"
              width={500}
              height={500}
              className=""
            />
          </div>
        </div>
      </section>
    </div>
  );
}
