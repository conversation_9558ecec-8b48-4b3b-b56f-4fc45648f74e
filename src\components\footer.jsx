"use client";

import Link from "next/link";


export default function Footer() {
  return (
    <>
      <footer className="bg-stone-950 border-t border-gray-300 py-6 px-4 md:px-12">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          {/* Left: Site Name/Link */}
          <div>
            <Link
              href="https://foodforsoul.in/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-teal-400 font-bold text-xl hover:underline"
            >
              foodforsoul.in
            </Link>
          </div>

          {/* Right: Menu Links */}
          <div className="flex flex-col md:flex-row items-start md:items-center gap-4 text-white text-sm">
            <ul className="flex gap-4">
              <li>
                <Link href="/" className="hover:text-teal-400">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/about" className="hover:text-teal-400">
                  About
                </Link>
              </li>
              <li>
                <Link href="/team" className="hover:text-teal-400">
                  Team
                </Link>
              </li>
              <li>
                <Link href="/pricing" className="hover:text-teal-400">
                  Pricing
                </Link>
              </li>
              <li>
                <Link href="/#register" className="hover:text-teal-400">
                  Register
                </Link>
              </li>
            </ul>

            <ul className="flex gap-4">
              <li>
                <Link href="/terms-and-conditions" className="hover:text-teal-400">
                  Terms & Conditions
                </Link>
              </li>
              <li>
                <Link href="/privacy-policy" className="hover:text-teal-400">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/cancellation-and-refund-policy" className="hover:text-teal-400">
                  Cancellation & Refund
                </Link>
              </li>
              <li>
                <Link href="/shipping-and-delivery" className="hover:text-teal-400">
                  Shipping & Delivery
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-teal-400">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="text-center text-gray-500 text-sm mt-4">
          &copy; {new Date().getFullYear()} Food For Soul. All rights reserved.
        </div>
      </footer>
    </>
  );
}
