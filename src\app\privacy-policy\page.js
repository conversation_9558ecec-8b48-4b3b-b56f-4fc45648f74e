"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function PrivacyPolicy() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false, // useful for debugging
            },
          }
        );
      });
    });
  }, [isClient]);

  return (
    <div className="bg-primary theme-secondary-font">
      <section
        className="bg-teal-100 py-16 text-center bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div className="max-w-6xl mx-auto px-4" ref={heroRef}>
          <h2 className="text-4xl md:text-6xl font-bold text-teal-600 mb-12 theme-primary-font">
            PRIVACY POLICY
          </h2>
          <div className="max-w-4xl mx-auto px-4 text-left text-gray-800 space-y-6 py-10">
            <p>
              <strong>Effective Date:</strong> 26 May 2025
            </p>
            <p>
              Introduction: At Food For Soul (FFS), we are committed to
              safeguarding your personal data. This Privacy Policy outlines how
              we collect, use, and protect your information when you use our
              website, services, mobile app, and communication channels.
            </p>
            <p>
              Information We Collect: We may collect the following types of
              personal information:
            </p>
            <p>
              Basic Details: Name, phone number, email address, age, gender,
              city.
            </p>
            <p>
              Health & Fitness Data: Weight, height, medical history (if
              shared), fitness goals, and dietary preferences.
            </p>
            <p>
              Payment Information: Billing address and transaction details
              (processed via secure third-party gateways).
            </p>
            <p>
              Usage Data: IP address, device type, browser type, and interaction
              with the website.
            </p>
            <p>
              3. How We Use Your Information Your data is used to:
              <br />
              Create customized fitness and diet plans.
              <br />
              Assign personal coaches and track your progress.
              <br />
              Send health tips, updates, and promotional content. Improve our
              products, services, and user experience.
              <br />
              Comply with legal requirements and security protocols.
            </p>
            <p>
              Data Sharing & Third Parties: We do not sell your personal data.
              We may share limited information only with:
            </p>
            <p>
              Our team of certified coaches or dieticians for personalized
              services.
            </p>
            <p>
              Trusted service providers (payment processors, CRM tools,
              SMS/email tools) under confidentiality agreements.
            </p>
            <p>
              Data Security We implement industry-standard security measures to
              protect your data from unauthorized access, alteration, or
              disclosure.
            </p>
            <p>
              Access, correct, or delete your personal information. Withdraw
              consent for communications at any time. Request your data report
              by emailing us at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-teal-600 underline"
              >
                <EMAIL>
              </a>{" "}
            </p>
            <p>
              Cookies Policy: We use cookies to enhance your browsing
              experience. You can choose to accept or reject cookies via your
              browser settings.
            </p>
            <p>
              Updates to This Policy We may occasionally update this Privacy
              Policy. All changes will be posted on this page with the updated
              effective date.
            </p>
            <p>
              Contact Us For any queries related to privacy or data
              protection, please contact:{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-teal-600 underline"
              >
                <EMAIL>
              </a>{" "}
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
