import { NextResponse } from "next/server";

export async function POST(request) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader) {
      return NextResponse.json(
        { error: "Authorization header required" },
        { status: 401 }
      );
    }

    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
      await request.json();

    console.log("Verification request data:", {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      authHeader,
    });

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      return NextResponse.json(
        { error: "Missing required payment verification parameters" },
        { status: 400 }
      );
    }

    const apiUrl = `${process.env.API_BASE_URL}/api/payments/verify`;
    console.log("Calling backend API:", apiUrl);

    // Use POST method to send the verification data
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: authHeader,
      },
      body: JSON.stringify({
        razorpay_order_id,
        razorpay_payment_id,
        razorpay_signature,
      }),
    });

    console.log("Backend response status:", response.status);
    console.log("Backend response headers:", response.headers);

    const data = await response.json();
    console.log("Backend response data:", data);

    if (!response.ok) {
      console.log("Backend error response:", data);

      // Handle specific error cases
      let errorMessage = data.message || "Payment verification failed";

      if (response.status === 400) {
        if (errorMessage.includes("Payment already verified")) {
          return NextResponse.json(
            {
              status: "Success",
              message: "Payment already verified",
              data: {
                order: { status: "SUCCESS" },
                subscription: null,
              },
            },
            { status: 200 }
          );
        }
      }

      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    console.log("Verification successful, returning:", data);
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error("Error verifying payment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
