const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";

// API utility functions
export const apiClient = {
  async login(credentials) {
    const response = await fetch("/api/auth/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Login failed");
    }

    return response.json();
  },

  async getPlans(token) {
    const response = await fetch("/api/plans", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to fetch plans");
    }

    const result = await response.json();
    return result.data;
  },

  async createOrder(orderData, token) {
    const response = await fetch("/api/order", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(orderData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to create order");
    }

    return response.json();
  },

  async verifyPayment(
    razorpay_order_id,
    razorpay_payment_id,
    razorpay_signature,
    token
  ) {
    const response = await fetch("/api/verify", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        razorpay_order_id,
        razorpay_payment_id,
        razorpay_signature,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Payment verification failed");
    }

    return response.json();
  },
};

// Local storage utilities for auth
export const authStorage = {
  setAuth(data) {
    localStorage.setItem("auth", JSON.stringify(data));
  },

  getAuth() {
    const auth = localStorage.getItem("auth");
    return auth ? JSON.parse(auth) : null;
  },

  clearAuth() {
    localStorage.removeItem("auth");
  },

  isAuthenticated() {
    const auth = this.getAuth();
    if (!auth) return false;

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    return auth.accessTokenExp > now;
  },

  getToken() {
    const auth = this.getAuth();
    return auth?.accessToken || null;
  },
};
