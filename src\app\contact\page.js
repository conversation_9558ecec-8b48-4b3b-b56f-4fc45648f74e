"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function Contact() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false, // useful for debugging
            },
          }
        );
      });
    });
  }, [isClient]);

  return (
    <div className="bg-primary theme-secondary-font">
      <section
        className="bg-teal-100 py-16 text-center bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div className="max-w-6xl mx-auto px-4" ref={heroRef}>
          <h2 className="text-4xl md:text-6xl font-bold text-teal-600 mb-12 theme-primary-font">
            CONTACT US
          </h2>
          <div className="max-w-3xl mx-auto px-4 py-10 text-gray-800 space-y-6">
            <p>
              We’d love to hear from you! Whether you have a question about our
              fitness plans, need help with your subscription, or just want to
              say hi — we’re here for you.
            </p>

            <div>
              <p className="font-semibold">Call or WhatsApp Us</p>
              <p>
                +91-8827342547
                <br />
                Available: Mon–Sat, 10 AM to 7 PM
              </p>
            </div>

            <div>
              <p className="font-semibold">Email Us</p>
              <p>
                <a
                  href="mailto:<EMAIL>"
                  className="text-teal-600 underline"
                >
                  <EMAIL>
                </a>
                <br />
                Response Time: Within 24–48 hours
              </p>
            </div>

            <div>
              <p className="font-semibold">Follow Us Online</p>
              <p>
                Instagram | Facebook | LinkedIn
                <br />
                <a
                  href="https://www.foodforsoul.in"
                  className="text-teal-600 underline"
                >
                  www.foodforsoul.in
                </a>
              </p>
            </div>

            <div>
              <p className="font-semibold">
                Office Address (for communication only):
              </p>
              <p>
                Food For Soul (FFS)
                <br />
                Mumbai, Maharashtra
                <br />
                India – 400056
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
