"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function CancellationAndRefundPolicy() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false, // useful for debugging
            },
          }
        );
      });
    });
  }, [isClient]);

  return (
    <div className="bg-primary theme-secondary-font">
      <section
        className="bg-teal-100 py-16 text-center bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div className="max-w-6xl mx-auto px-4" ref={heroRef}>
          <h2 className="text-4xl md:text-6xl font-bold text-teal-600 mb-12 theme-primary-font">
            CANCELLATION AND REFUND POLICY
          </h2>
          <div className="max-w-4xl mx-auto px-4 text-left text-gray-800 space-y-6 py-10">
            <p>
              <strong>Effective Date:</strong> 26 May 2025
            </p>
            <p>
              <strong>Applies To:</strong> All services offered by Food For Soul
              (FFS) via www.foodforsoul.in
            </p>

            <p>
              <strong>General Policy:</strong> No refunds once service has
              started due to the personalized nature of offerings.
            </p>

            <p>
              <strong>Cancellation of Services:</strong>
              <br />
              <em>Before Plan Activation:</em> Refund possible within 24 hours
              of payment minus 10% fee.
              <br />
              <em>After Plan Activation:</em> No refund or cancellation.
              <br />
              <em>Digital Products:</em> No refund on e-books, courses, or
              downloads.
            </p>

            <p>
              <strong>Coaching Delay or Non-Response:</strong> Delay beyond 7
              working days allows for full refund or reschedule.
            </p>

            <p>
              <strong>Client Inactivity:</strong> No response within 15 days
              marks service as consumed. Can reactivate within 45 days.
            </p>

            <p>
              <strong>EMI/Instalments:</strong> Cancellation only before
              first consultation. No refund after month begins.
            </p>

            <p>
              <strong>Cancellation Request:</strong> Email{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-teal-600 underline"
              >
                <EMAIL>
              </a>{" "}
              within 24 hours of purchase with name, contact, payment proof, and
              reason.
            </p>

            <p>
              <strong>Refund Timeline:</strong> Approved refunds processed in
              7–10 working days via original payment method.
            </p>

            <p>
              <strong>Final Decision:</strong> All refunds subject to
              approval by FFS management.
            </p>

            <p>
              Contact:{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-teal-600 underline"
              >
                <EMAIL>
              </a>
              , 📞 +91-8827342547
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
