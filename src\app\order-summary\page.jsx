"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  CheckCircle2,
  LoaderCircle,
  ArrowLeft,
  Tag,
  Sparkles,
  CreditCard,
  Shield,
} from "lucide-react";
import { apiClient, authStorage, Plan } from "@/lib/api";
import Header from "@/components/Header";

export default function OrderSummaryPage() {
  const router = useRouter();
  const params = useSearchParams();
  const planId = params.get("planId");
  const durationId = params.get("durationId");
  const price = params.get("price");

  const [user, setUser] = useState < any > null;
  const [plan, setPlan] = (useState < Plan) | (null > null);
  const [selectedDuration, setSelectedDuration] = useState < any > null;
  const [couponCode, setCouponCode] = useState("");
  const [couponValidation, setCouponValidation] = useState < any > null;
  const [loading, setLoading] = useState(true);
  const [validatingCoupon, setValidatingCoupon] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const auth = authStorage.getAuth();
    if (!auth || !authStorage.isAuthenticated()) {
      router.replace("/login");
      return;
    }

    if (!planId || !durationId || !price) {
      router.replace("/");
      return;
    }

    setUser(auth);

    // Only fetch if we don't already have the plan data
    if (!plan) {
      fetchPlanDetails(auth.accessToken);
    }
  }, [planId, durationId, price, plan]);

  const fetchPlanDetails = async (token) => {
    try {
      setLoading(true);
      const plansData = await apiClient.getPlans(token);
      const currentPlan = plansData.find((p) => p.id === planId);

      if (!currentPlan) {
        setError("Plan not found");
        return;
      }

      const currentDuration = currentPlan.durations.find(
        (d) => d.id === durationId
      );

      if (!currentDuration) {
        setError("Duration not found");
        return;
      }

      setPlan(currentPlan);
      setSelectedDuration(currentDuration);
    } catch (error) {
      setError("Failed to fetch plan details");
    } finally {
      setLoading(false);
    }
  };

  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      setCouponValidation(null);
      return;
    }

    try {
      setValidatingCoupon(true);
      const response = await fetch("/api/validate-coupon", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user.accessToken}`,
        },
        body: JSON.stringify({
          code: couponCode,
          planId,
          durationId,
          userId: user._id || user.id,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setCouponValidation({
          valid: true,
          ...result,
        });
      } else {
        setCouponValidation({
          valid: false,
          error: result.error || "Invalid coupon",
        });
      }
    } catch (error) {
      setCouponValidation({
        valid: false,
        error: "Failed to validate coupon",
      });
    } finally {
      setValidatingCoupon(false);
    }
  };

  const [isNavigating, setIsNavigating] = useState(false);

  const proceedToCheckout = () => {
    if (isNavigating) return; // Prevent double clicks

    setIsNavigating(true);
    const finalPrice = couponValidation?.valid
      ? couponValidation.data.finalAmount
      : selectedDuration?.price;
    router.push(
      `/checkout?planId=${planId}&durationId=${durationId}&price=${finalPrice}&couponCode=${couponCode}`
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
        <Header user={user} />
        <div className="flex justify-center items-center h-[80vh]">
          <div className="text-center">
            <LoaderCircle className="animate-spin h-16 w-16 text-purple-600 mx-auto" />
            <p className="text-gray-600 font-medium">
              Loading order details...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !plan || !selectedDuration) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
        <Header user={user} />
        <div className="flex flex-col justify-center items-center h-[80vh] gap-6">
          <div className="text-center max-w-md">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-600 text-2xl">⚠️</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Plan Not Found
            </h2>
            <p className="text-red-600 mb-6">
              {error || "Plan details not found"}
            </p>
            <Button onClick={() => router.push("/")} className="btn-gradient">
              Back to Plans
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const originalPrice = selectedDuration.price;

  const finalPrice = couponValidation?.valid
    ? couponValidation.data.finalAmount
    : originalPrice;

  const discount = couponValidation?.valid ? couponValidation.data.discount : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
      <Header user={user} />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
            Order Summary
          </h1>
          <p className="text-base md:text-lg text-gray-600">
            Review your plan details and apply any coupon codes before
            proceeding to payment.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {/* Plan Details */}
          <Card>
            <CardHeader>
              <CardTitle>{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-semibold">{selectedDuration.label}</h4>
                  <span className="text-2xl font-bold">
                    ₹{originalPrice.toLocaleString()}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">
                  {selectedDuration.valueInDays} days •{" "}
                  {selectedDuration.paymentType}
                </p>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-medium">
                  What&apos;s included:
                </Label>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {selectedDuration.features.map((feature) => (
                    <div
                      key={feature.id}
                      className="flex items-start gap-3 text-sm"
                    >
                      <CheckCircle2 className="text-green-500 h-4 w-4 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium">{feature.name}</div>
                        <div className="text-muted-foreground text-xs">
                          {feature.description}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Summary & Coupon */}
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Coupon Section */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">
                  <Tag className="inline mr-2 h-4 w-4" />
                  Have a coupon code?
                </Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter coupon code"
                    value={couponCode}
                    onChange={(e) =>
                      setCouponCode(e.target.value.toUpperCase())
                    }
                    className="flex-1"
                  />
                  <Button
                    onClick={validateCoupon}
                    disabled={validatingCoupon || !couponCode.trim()}
                    variant="outline"
                  >
                    {validatingCoupon ? (
                      <LoaderCircle className="h-4 w-4 animate-spin" />
                    ) : (
                      "Apply"
                    )}
                  </Button>
                </div>

                {couponValidation && (
                  <div
                    className={`text-sm p-2 rounded ${
                      couponValidation.valid
                        ? "bg-green-50 text-green-700 border border-green-200"
                        : "bg-red-50 text-red-700 border border-red-200"
                    }`}
                  >
                    {couponValidation.valid
                      ? `Coupon applied! You save ₹${couponValidation.data.discount.toLocaleString()}`
                      : couponValidation.error}
                  </div>
                )}
              </div>

              {/* Price Breakdown */}
              <div className="space-y-3 border-t pt-4">
                <div className="flex justify-between">
                  <span>Plan Price</span>
                  <span>₹{originalPrice.toLocaleString()}</span>
                </div>

                {discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount</span>
                    <span>-₹{discount.toLocaleString()}</span>
                  </div>
                )}

                <div className="flex justify-between text-lg font-bold border-t pt-2">
                  <span>Total</span>
                  <span>₹{finalPrice.toLocaleString()}</span>
                </div>
              </div>

              <Button
                onClick={proceedToCheckout}
                disabled={isNavigating}
                className="w-full h-12 text-base font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isNavigating ? "Redirecting..." : "Proceed to Payment"}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
