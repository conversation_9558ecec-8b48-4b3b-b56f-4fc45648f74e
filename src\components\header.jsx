"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";
import Link from "next/link";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const mobileMenuRef = useRef(null);

  // Function to open the menu
  const openMenu = () => {
    setIsMenuOpen(true);
  };

  // Function to close the menu
  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const pathname = usePathname();

  const linkClasses = (href) =>
    `relative text-white hover:text-primary transition pb-3 ${
      pathname === href
        ? "after:absolute after:bottom-0 after:left-0 after:w-full after:h-[2px] after:bg-teal-400"
        : ""
    }`;

  return (
    <>
      {/* Fixed Header */}
      <header className="fixed top-4 left-0 w-full flex justify-between items-center px-6 z-10">
        {/* Logo */}
        <div className="text-white font-heading text-xl">
          <img src="/images/logo-3.png" alt="Logo" className="h-16 sm:h-20" />
        </div>

        {/* Desktop Nav */}
        <nav className="hidden md:flex bg-stone-900/30 backdrop-blur-md px-6 pt-2 rounded-full space-x-9 text-sm font-medium">
          <Link href="/" className={linkClasses("/")}>
            Home
          </Link>
          <Link href="/about" className={linkClasses("/about")}>
            About
          </Link>
          <Link href="/team" className={linkClasses("/team")}>
            Team
          </Link>
          <Link href="/pricing" className={linkClasses("/pricing")}>
            Pricing
          </Link>
          <Link href="/#register" className="text-white hover:text-primary transition">
            Register
          </Link>
        </nav>

        {/* Hamburger Icon */}
        <button
          id="menuToggle"
          className={`md:hidden text-white focus:outline-none z-50 bg-stone-900/30 rounded-full p- ${
            isMenuOpen ? "hidden" : "block"
          }`}
          onClick={openMenu}
        >
          <svg
            className="w-8 h-8"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </header>

      {/* Mobile Sidebar */}
      <div
        ref={mobileMenuRef}
        id="mobileMenu"
        className={`fixed top-0 p-10 z-10 right-0 w-64 h-full bg-stone-800/40 backdrop-blur transform transition-transform duration-300 ease-in-out ${
          isMenuOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <button
          id="closeMenu"
          onClick={closeMenu}
          className="self-end text-white"
        >
          ✕
        </button>
        <Link
          href="/"
          className="text-white transition block w-full my-10"
          onClick={closeMenu}
        >
          Home
        </Link>
        <Link
          href="/about"
          className="text-white transition block w-full my-10"
          onClick={closeMenu}
        >
          About
        </Link>
        <Link
          href="/team"
          className="text-white transition block w-full my-10"
          onClick={closeMenu}
        >
          Team
        </Link>
        <Link
          href="/pricing"
          className="text-white transition block w-full my-10"
          onClick={closeMenu}
        >
          Pricing
        </Link>
        <Link
          href="/#register"
          className="text-white transition block w-full my-10"
          onClick={closeMenu}
        >
          Register
        </Link>
      </div>
    </>
  );
}
