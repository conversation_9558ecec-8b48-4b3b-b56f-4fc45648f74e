{"name": "food-for-soul", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "aos": "^2.3.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "gsap": "^3.13.0", "next": "15.3.2", "razorpay": "^2.9.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "24.0.3", "@types/react": "19.1.8", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4"}}