"use client";

import * as React from "react";
import { ChevronDown, Check } from "lucide-react";
import { cn } from "@/lib/utils";

const Select = React.forwardRef(({ className, children, ...props }, ref) => (
  <div className={cn("relative", className)} ref={ref} {...props}>
    {children}
  </div>
));
Select.displayName = "Select";

const SelectTrigger = React.forwardRef(
  ({ className, children, ...props }, ref) => (
    <button
      className={cn(
        "flex h-12 w-full items-center justify-between rounded-xl border border-gray-200 bg-white px-4 py-3 text-sm font-medium text-gray-900 shadow-sm transition-all duration-200 hover:border-teal-300 hover:shadow-md focus:border-teal-400 focus:outline-none focus:ring-2 focus:ring-teal-200 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      ref={ref}
      {...props}
    >
      {children}
      <ChevronDown className="h-4 w-4 opacity-50 transition-transform duration-200" />
    </button>
  )
);
SelectTrigger.displayName = "SelectTrigger";

const SelectValue = React.forwardRef(({ className, ...props }, ref) => (
  <span
    className={cn("block truncate text-left", className)}
    ref={ref}
    {...props}
  />
));
SelectValue.displayName = "SelectValue";

const SelectContent = React.forwardRef(
  ({ className, children, position = "popper", ...props }, ref) => (
    <div
      className={cn(
        "absolute z-50 mt-2 w-full overflow-hidden rounded-xl border border-gray-200 bg-white shadow-lg animate-in fade-in-0 zoom-in-95",
        position === "popper" &&
          "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
        className
      )}
      ref={ref}
      {...props}
    >
      <div className="max-h-60 overflow-auto custom-scrollbar p-1">
        {children}
      </div>
    </div>
  )
);
SelectContent.displayName = "SelectContent";

const SelectItem = React.forwardRef(
  ({ className, children, selected, ...props }, ref) => (
    <div
      className={cn(
        "relative flex w-full cursor-pointer select-none items-center rounded-lg px-3 py-3 text-sm outline-none transition-colors duration-150 hover:bg-teal-50 hover:text-teal-900 focus:bg-teal-50 focus:text-teal-900",
        selected && "bg-teal-100 text-teal-900 font-medium",
        className
      )}
      ref={ref}
      {...props}
    >
      <span className="flex-1">{children}</span>
      {selected && (
        <Check className="h-4 w-4 text-teal-600 ml-2 flex-shrink-0" />
      )}
    </div>
  )
);
SelectItem.displayName = "SelectItem";

const SelectLabel = React.forwardRef(({ className, ...props }, ref) => (
  <div
    className={cn(
      "px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider",
      className
    )}
    ref={ref}
    {...props}
  />
));
SelectLabel.displayName = "SelectLabel";

const SelectSeparator = React.forwardRef(({ className, ...props }, ref) => (
  <div
    className={cn("mx-1 my-1 h-px bg-gray-200", className)}
    ref={ref}
    {...props}
  />
));
SelectSeparator.displayName = "SelectSeparator";

export {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
  SelectLabel,
  SelectSeparator,
};
