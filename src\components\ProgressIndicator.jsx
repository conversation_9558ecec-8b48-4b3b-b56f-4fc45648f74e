"use client";

import { Check } from "lucide-react";

export default function ProgressIndicator({ currentStep, steps }) {
  return (
    <div className="w-full py-8">
      <div className="flex items-center justify-between max-w-4xl mx-auto px-4">
        {steps.map((step, index) => {
          const stepNumber = index + 1;
          const isCompleted = stepNumber < currentStep;
          const isActive = stepNumber === currentStep;
          const isUpcoming = stepNumber > currentStep;

          return (
            <div key={step.id} className="flex items-center">
              {/* Step Circle */}
              <div className="flex flex-col items-center">
                <div
                  className={`progress-step relative ${
                    isCompleted ? "completed" : ""
                  } ${isActive ? "active" : ""} ${
                    isUpcoming ? "opacity-50" : ""
                  }`}
                >
                  {isCompleted ? (
                    <Check className="h-6 w-6" />
                  ) : (
                    <span className="text-sm font-bold">{stepNumber}</span>
                  )}

                  {/* Pulse animation for active step */}
                  {isActive && (
                    <div className="absolute inset-0 rounded-full bg-purple-500 animate-ping opacity-20"></div>
                  )}
                </div>

                {/* Step Info */}
                <div className="mt-3 text-center max-w-24">
                  <p
                    className={`text-xs font-medium ${
                      isActive
                        ? "text-purple-600"
                        : isCompleted
                        ? "text-green-600"
                        : "text-gray-500"
                    }`}
                  >
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-400 mt-1 hidden sm:block">
                    {step.description}
                  </p>
                </div>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="flex-1 mx-4 h-0.5 bg-gray-200 relative">
                  <div
                    className={`h-full transition-all duration-500 ease-in-out ${
                      isCompleted
                        ? "bg-gradient-to-r from-green-500 to-emerald-500 w-full"
                        : isActive
                        ? "bg-gradient-to-r from-purple-500 to-pink-500 w-1/2 animate-pulse"
                        : "w-0"
                    }`}
                  ></div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
