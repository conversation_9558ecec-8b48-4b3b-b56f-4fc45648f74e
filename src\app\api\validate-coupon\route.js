import { NextResponse } from "next/server";

export async function POST(request) {
  try {
    const authHeader = request.headers.get("authorization");
    console.log("authHeader: ", authHeader);
    if (!authHeader) {
      return NextResponse.json(
        { error: "Authorization header required" },
        { status: 401 }
      );
    }

    const { code, planId, durationId } = await request.json();
    console.log("code, planId, durationId: ", code, planId, durationId);

    if (!code || !planId || !durationId) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    const apiUrl = `${process.env.API_BASE_URL}/api/coupons/validate`;

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: authHeader,
      },
      body: JSON.stringify({
        code,
        planId,
        durationId,
      }),
    });

    const data = await response.json();
    console.log("data: ", data);

    if (!response.ok) {
      return NextResponse.json(
        { error: data.message || "Failed to validate coupon" },
        { status: response.status }
      );
    }

    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error("Error validating coupon:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
