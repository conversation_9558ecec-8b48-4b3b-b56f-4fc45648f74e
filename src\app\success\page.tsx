"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { apiClient, authStorage } from "@/lib/api";
import { Button } from "@/components/ui/button";

export default function SuccessPage() {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const auth = authStorage.getAuth();
    if (!auth || !authStorage.isAuthenticated()) {
      router.replace("/login");
      return;
    }
    setUser(auth);
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 max-w-4xl">
        <div className="text-center space-y-8">
          {/* Success Animation */}
          <div className="flex justify-center mb-8">
            <div className="success-checkmark animate-scale-in"></div>
          </div>

          {/* Main Success Message */}
          <div className="space-y-4 animate-slide-up">
            <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-green-600 via-emerald-600 to-green-700 bg-clip-text text-transparent">
              Payment Successful!
            </h1>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
              🎉 Congratulations, {user?.firstName}! Your fitness journey starts
              now.
            </p>
          </div>

          {/* Success Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 animate-slide-up">
            <div className="gradient-card rounded-2xl p-6 text-center hover:scale-105 transition-transform duration-300">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"></div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Check Your Email
              </h3>
              <p className="text-sm text-gray-600">
                Plan details and access instructions have been sent to your
                email.
              </p>
            </div>

            <div className="gradient-card rounded-2xl p-6 text-center hover:scale-105 transition-transform duration-300">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4"></div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Download Our App
              </h3>
              <p className="text-sm text-gray-600">
                Get the mobile app for the best workout experience on the go.
              </p>
            </div>

            <div className="gradient-card rounded-2xl p-6 text-center hover:scale-105 transition-transform duration-300">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"></div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Start Training
              </h3>
              <p className="text-sm text-gray-600">
                Your personalized workout plan is ready. Let's achieve your
                goals!
              </p>
            </div>
          </div>

          {/* What's Next Section */}
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-3xl p-8 mt-12 animate-slide-up">
            <div className="flex items-center justify-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">What's Next?</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left max-w-3xl mx-auto">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      Immediate Access
                    </h4>
                    <p className="text-sm text-gray-600">
                      Your plan is active and ready to use right now
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      Expert Support
                    </h4>
                    <p className="text-sm text-gray-600">
                      24/7 chat support and consultation calls included
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      Progress Tracking
                    </h4>
                    <p className="text-sm text-gray-600">
                      Monitor your fitness journey with detailed analytics
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  \{" "}
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      Community Access
                    </h4>
                    <p className="text-sm text-gray-600">
                      Join our exclusive fitness community
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-12 animate-slide-up">
            <Button
              onClick={() => router.push("/")}
              className="btn-gradient flex items-center gap-2 h-12 px-8"
            >
              Back to Home
            </Button>
            <Button
              variant="outline"
              className="border-purple-200 text-purple-700 hover:bg-purple-50 h-12 px-8"
            >
              Download App
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
