"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function TeamPage() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const addToRefs = (el) => {
    if (el && !sectionRefs.current.includes(el)) {
      sectionRefs.current.push(el);
    }
  };

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false, // useful for debugging
            },
          }
        );
      });
    });
  }, [isClient]);

  return (
    <div className="bg-primary theme-secondary-font">
      <section
        className="bg-teal-100 py-16 text-center bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div className="max-w-6xl mx-auto px-4" ref={heroRef}>
          <h2 className="text-4xl md:text-6xl font-bold text-teal-600 mb-12 theme-primary-font">
            THE TEAM
          </h2>

          <div className="flex flex-wrap justify-center gap-x-8 gap-y-12">
            {/* Member 1 */}
            <div className="w-full sm:w-1/3 md:w-1/4 flex flex-col items-center">
              <img
                src="/images/member-1.png"
                className="rounded-2xl w-full max-w-xs"
              />
              <h3 className="mt-4 font-semibold text-2xl text-teal-600 theme-primary-font">Ashutosh</h3>
              <p className="text-sm text-gray-600 font-semibold">Founder/Coach</p>
            </div>

            {/* Member 6 */}
            <div className="w-full sm:w-1/3 md:w-1/4 flex flex-col items-center">
              <img
                src="/images/monil.jpg"
                className="rounded-2xl w-full max-w-xs"
              />
              <h3 className="mt-4 font-semibold text-2xl text-teal-600 theme-primary-font">Monil Chouhan</h3>
              <p className="text-sm text-gray-600 font-semibold">Coach</p>
            </div>

            {/* Member 5 */}
            <div className="w-full sm:w-1/3 md:w-1/4 flex flex-col items-center">
              <img
                src="/images/nitin.jpg"
                className="rounded-2xl w-full max-w-xs"
              />
              <h3 className="mt-4 font-semibold text-2xl text-teal-600 theme-primary-font">Nitin Khare</h3>
              <p className="text-sm text-gray-600 font-semibold">Operations</p>
            </div>

            {/* Member 4 */}
            <div className="w-full sm:w-1/3 md:w-1/4 flex flex-col items-center">
              <img
                src="/images/member-4.png"
                className="rounded-2xl w-full max-w-xs"
              />
              <h3 className="mt-4 font-semibold text-2xl text-teal-600 theme-primary-font">Sachin Shrivastava</h3>
              <p className="text-sm text-gray-600 font-semibold">IT</p>
            </div>

            {/* Member 2 */}
            <div className="w-full sm:w-1/3 md:w-1/4 flex flex-col items-center">
              <img
                src="/images/member-2.png"
                className="rounded-2xl w-full max-w-xs"
              />
              <h3 className="mt-4 font-semibold text-2xl text-teal-600 theme-primary-font">Srishti Madan</h3>
              <p className="text-sm text-gray-600 font-semibold">Dietician</p>
            </div>

            {/* Member 3 */}
            <div className="w-full sm:w-1/3 md:w-1/4 flex flex-col items-center">
              <img
                src="/images/member-3.png"
                className="rounded-2xl w-full max-w-xs"
              />
              <h3 className="mt-4 font-semibold text-2xl text-teal-600 theme-primary-font">Akanksha Aggarval</h3>
              <p className="text-sm text-gray-600 font-semibold">Dietician</p>
            </div>

          </div>
        </div>
      </section>

    </div>
  );
}
