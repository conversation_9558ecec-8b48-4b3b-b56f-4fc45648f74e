"use client";

import { LoaderCircle } from "lucide-react";

export default function LoadingSpinner({
  message = "Loading...",
  size = "md",
  variant = "primary",
}) {
  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-16 w-16",
    lg: "h-24 w-24",
  };

  const colorClasses = {
    primary: "text-purple-600",
    secondary: "text-gray-600",
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      <div className="animate-pulse-glow">
        <LoaderCircle
          className={`animate-spin ${sizeClasses[size]} ${colorClasses[variant]}`}
        />
      </div>
      <p className="text-gray-600 font-medium animate-pulse">{message}</p>
    </div>
  );
}
