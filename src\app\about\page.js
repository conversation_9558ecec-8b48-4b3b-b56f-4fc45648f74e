"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Link from "next/link";

import { CheckIcon } from "@heroicons/react/24/solid";

gsap.registerPlugin(ScrollTrigger);

export default function AboutPage() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const addToRefs = (el) => {
    if (el && !sectionRefs.current.includes(el)) {
      sectionRefs.current.push(el);
    }
  };

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false, // useful for debugging
            },
          }
        );
      });
    });
  }, [isClient]);

  return (
    <div className="bg-primary theme-secondary-font text-black pt-5">
      {/* About Company Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold text-center text-teal-600 theme-primary-font">
            About the Company – Food for Soul (FFS)
          </h1>
          <p className="text-xl text-center">
            Food for Soul (FFS) began as a humble diet café in Indore, India,
            with a simple mission: to nourish both body and spirit. Founded by
            Ashutosh Srivastava—a fitness coach, certified nutritionist, and
            transformation specialist—FFS has grown from a local health
            initiative to a full-fledged wellness brand offering personalized
            diet and fitness solutions.
          </p>
          <p className="text-xl text-center">
            At FFS, we blend ancient wisdom with modern science to offer
            practical, effective, and sustainable health guidance. Whether
            you&apos;re aiming to lose weight, manage a medical condition, or build
            strength, our holistic approach is designed to fit your lifestyle.
          </p>
          <p className="text-xl text-center">
            With over 1000+ successful client transformations, FFS is not just a
            brand—it&apos;s a movement toward mindful living. Our belief:{" "}
            <span className="italic font-semibold">
              &quot;Fitness is not a phase; it&apos;s a foundation.&quot;
            </span>
          </p>
        </div>
      </section>

      {/* App Coming Soon Section */}
      <section className="py-16 px-4 bg-teal-50">
        <div className="max-w-6xl mx-auto text-center space-y-6">
          <h2 className="text-3xl md:text-4xl font-bold text-teal-600 theme-primary-font">
            Coming Soon – Food for Soul Mobile App
          </h2>
          <p className="text-lg">
            Launching in <strong>July 2025</strong>, our official app will take
            your wellness journey to the next level!
          </p>

          <div className="grid md:grid-cols-2 gap-6 text-left text-lg max-w-4xl mx-auto mt-6">
            {[
              "Personalised Diet Plans – Based on your age, goals, lifestyle, and medical history.",
              "Home + Gym Workout Programs – Tailored routines with video guidance.",
              "Progress Tracker – Track your weight, water intake, steps, and inches weekly.",
              "Direct Coach Support – In-app chat and call feature to connect with your assigned coach.",
              "Live Webinars & Workshops – Monthly sessions with experts.",
              "Fitness Courses with Certificates – Learn the science of fitness, nutrition, and more.",
              "Community Challenges – Join transformation contests, earn points & rewards.",
              "Bilingual Support – Available in English and Hindi for ease of use.",
            ].map((feature, index) => (
              <div key={index} className="flex items-start gap-3">
                <CheckIcon className="w-5 h-5 mt-1 text-teal-600 flex-shrink-0" />
                <span>{feature}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Ashutosh Section (Founder Frame) */}
      <section
        className="bg-teal-100 py-16 text-center bg-cover bg-center flex items-center justify-center sm:h-screen"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div ref={heroRef}>
          <div className="max-w-6xl mx-auto px-4 grid md:grid-cols-2 gap-8 mt-10">
            {/* Left Column: Image */}
            <div className="w-full">
              <Image
                src="/images/founder.jpg"
                alt="Ashutosh Srivastava"
                width={0}
                height={0}
                sizes="100vw"
                className="w-full h-auto rounded-3xl"
              />
            </div>

            {/* Right Column: Text */}
            <div className="text-center md:text-left">
              <h2 className="text-4xl md:text-6xl font-bold text-accent mb-4 theme-primary-font text-teal-600">
                ASHUTOSH
              </h2>
              <ul className="space-y-4 text-left text-xl">
                {[
                  "Certified Personal Trainer & Dietician who has helped 1000+ clients over a span of 10 years to lose/gain at least 10 kgs of body weight through both online and offline training and consultations.",
                  "Successfully ran a Diet Cafe for 3 years (2017–2020) — until the Covid pandemic struck.",
                  "Former coach at HealthifyMe.",
                  "Trained high-end clients including doctors, politicians, and actors.",
                  "Published author and motivational speaker.",
                  "Well-connected with a wide professional network across the nation.",
                ].map((point, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <CheckIcon className="w-6 h-6 text-teal-600 mt-1 flex-shrink-0" />
                    <span>{point}</span>
                  </li>
                ))}
              </ul>
              <Link
                href="/#register"
                className="mt-10 inline-block bg-teal-600 text-white font-bold py-3 px-6 rounded-full hover:bg-accent-dark transition"
              >
                REGISTER NOW
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
