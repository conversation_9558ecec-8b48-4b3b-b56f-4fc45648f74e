"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function TermsAndConditions() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const addToRefs = (el) => {
    if (el && !sectionRefs.current.includes(el)) {
      sectionRefs.current.push(el);
    }
  };

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false, // useful for debugging
            },
          }
        );
      });
    });
  }, [isClient]);

  return (
    <div className="bg-primary theme-secondary-font">
      <section
        className="bg-teal-100 py-16 text-center bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-4xl md:text-6xl font-bold text-teal-600 mb-12 theme-primary-font">
            SHIPPING & DELIVERY POLICY
          </h2>
          <div className="max-w-3xl mx-auto px-4 py-10 text-gray-800 space-y-6 text-start">
            <p>
              <strong>Effective Date:</strong> 26 May 2025
              <br />
              <strong>Applies To:</strong> All physical products sold via
              www.foodforsoul.in
            </p>

            <div>
              <h3 className="font-semibold">
                1. Digital vs Physical Deliverables
              </h3>
              <p>
                <strong>Digital Products:</strong> Delivered via email,
                WhatsApp, or dashboard within 48 hours.
                <br />
                <strong>Physical Products:</strong> Shipped via DTDC, Delhivery,
                India Post, etc. Tracking ID shared after dispatch.
              </p>
            </div>

            <div>
              <h3 className="font-semibold">2. Shipping Charges</h3>
              <p>
                <strong>Within India:</strong> Free shipping on prepaid orders
                over ₹499. ₹50 charge for orders below ₹499.
                <br />
                <strong>International Shipping:</strong> Not available.
              </p>
            </div>

            <div>
              <h3 className="font-semibold">3. Delivery Timeline (India)</h3>
              <ul className="list-disc list-inside">
                <li>
                  <strong>Metro Cities:</strong> 3–5 business days
                </li>
                <li>
                  <strong>Tier 2 & 3 Cities:</strong> 5–7 business days
                </li>
                <li>
                  <strong>Remote Areas:</strong> 7–10 business days
                </li>
              </ul>
              <p>May vary due to restrictions, holidays, or weather.</p>
            </div>

            <div>
              <h3 className="font-semibold">4. Order Processing Time</h3>
              <p>
                Processed within 24–48 working hours post-payment.
                Weekend/holiday orders processed next working day.
              </p>
            </div>

            <div>
              <h3 className="font-semibold">5. Delayed or Lost Shipments</h3>
              <p>
                Check for tracking ID in email/WhatsApp. For unresolved issues,
                contact{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-teal-600 underline"
                >
                  <EMAIL>
                </a>{" "}
                with your order ID.
              </p>
            </div>

            <div>
              <h3 className="font-semibold">6. Damaged or Wrong Product</h3>
              <p>
                Report within 2 days of delivery with photos. We will arrange a
                replacement or offer store credit/refund as per policy.
              </p>
            </div>

            <div>
              <h3 className="font-semibold">
                7. Delivery Address & Responsibility
              </h3>
              <p>
                Ensure correct address and phone number at checkout. FFS is not
                responsible for issues due to incorrect details.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
