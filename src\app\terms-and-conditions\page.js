"use client";

import { useEffect } from "react";
import { useState, useRef } from "react";
import Image from "next/image";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function TermsAndConditions() {
  const heroRef = useRef(null);
  const sectionRefs = useRef([]);
  sectionRefs.current = [];

  const addToRefs = (el) => {
    if (el && !sectionRefs.current.includes(el)) {
      sectionRefs.current.push(el);
    }
  };

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    requestAnimationFrame(() => {
      if (heroRef.current) {
        gsap.fromTo(
          heroRef.current,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 1.2, ease: "power3.out" }
        );
      }

      sectionRefs.current.forEach((section) => {
        if (!section) return;
        gsap.fromTo(
          section,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              toggleActions: "play none none reverse",
              markers: false, // useful for debugging
            },
          }
        );
      });
    });
  }, [isClient]);

  return (
    <div className="bg-primary theme-secondary-font">
      <section
        className="bg-teal-100 py-16 text-center bg-cover bg-center flex items-center justify-center"
        style={{ backgroundImage: "url(/images/bg.png)" }}
      >
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-4xl md:text-6xl font-bold text-teal-600 mb-12 theme-primary-font">
            TERMS & CONDITIONS
          </h2>
          <div className="max-w-4xl mx-auto px-4 text-left text-gray-800 space-y-6 py-10">
            <p>
              <strong>Effective Date:</strong> 26 May 2025
            </p>
            <p>
              By accessing or using www.foodforsoul.in or any services offered
              by Food For Soul (FFS), you agree to be bound by these Terms &
              Conditions. If you do not agree, please do not use the website or
              services.
            </p>
            <p>
              <strong>Services Provided:</strong> Customised diet and workout
              plans, online coaching, expert consultations, and informational
              content—all via online platforms.
            </p>
            <p>
              <strong>Eligibility:</strong> You must be 18+ to use our services.
              Minors require parental guidance.
            </p>
            <p>
              <strong>User Responsibilities:</strong> Provide accurate info,
              consult your doctor, follow FFS guidance, and avoid misuse or
              sharing confidential content.
            </p>
            <p>
              <strong>Payments & Refunds:</strong> All purchases are
              non-refundable unless stated. Ensure timely payment for
              uninterrupted service.
            </p>
            <p>
              <strong>Intellectual Property:</strong> All content is the
              property of FFS. Do not reproduce without written permission.
            </p>
            <p>
              <strong>Disclaimer:</strong> We offer guidance, not medical
              advice. Results vary by individual effort and lifestyle.
            </p>
            <p>
              <strong>Limitation of Liability:</strong> FFS is not responsible
              for any injury or health issues due to misuse of our content or
              services.
            </p>
            <p>
              <strong>Suspension/Termination:</strong> FFS may suspend or
              terminate access due to misuse or misconduct.
            </p>
            <p>
              <strong>Modifications:</strong> These terms may be updated.
              Continued use implies acceptance.
            </p>
            <p>
              <strong>Governing Law:</strong> Governed by Indian law; disputes
              fall under courts in Mumbai, Maharashtra.
            </p>
            <p>
              <strong>Contact:</strong> For queries, email us at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-teal-600 underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
