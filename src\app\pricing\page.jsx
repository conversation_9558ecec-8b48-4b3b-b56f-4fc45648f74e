"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  CheckCircle2,
  LoaderCircle,
  Star,
  Zap,
  ArrowRight,
  Sparkles,
  Eye,
  EyeOff,
  Crown,
  Target,
  Users,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { apiClient, authStorage } from "@/lib/api";
import Header from "@/components/header";
import Footer from "@/components/footer";

const PricingCard = ({ plan, onSelectPlan, isPopular = false }) => {
  const [selectedDurationIndex, setSelectedDurationIndex] = useState(0);
  const [showAllFeatures, setShowAllFeatures] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const selectedDuration = plan.durations?.[selectedDurationIndex];
  const visibleFeatures = showAllFeatures
    ? selectedDuration?.features || []
    : (selectedDuration?.features || []).slice(0, 4);

  // Safety check - if no duration is selected or available, return null
  if (!selectedDuration || !plan.durations?.length) {
    return (
      <div className="pricing-card animate-slide-up">
        <div className="text-center p-8">
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            Plan Unavailable
          </h3>
          <p className="text-gray-600">This plan is currently not available.</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`pricing-card ${isPopular ? "popular" : ""} animate-slide-up`}
    >
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          {isPopular ? (
            <Crown className="h-8 w-8 text-teal-600 mr-3" />
          ) : (
            <Target className="h-8 w-8 text-teal-600 mr-3" />
          )}
          <h3 className="text-2xl font-bold text-gray-900 theme-primary-font">
            {plan.name}
          </h3>
        </div>
        <p className="text-gray-600 leading-relaxed text-sm">
          {plan.description}
        </p>
      </div>

      {/* Duration Selector - Advanced Dropdown */}
      <div className="mb-8">
        <div className="flex items-center justify-center mb-4">
          <Sparkles className="h-4 w-4 text-teal-600 mr-2" />
          <span className="text-sm font-semibold text-gray-700">
            Select Duration:
          </span>
        </div>

        <Select
          value={selectedDuration.id}
          onValueChange={(value) => {
            const index = plan.durations.findIndex((d) => d.id === value);
            setSelectedDurationIndex(index);
            setIsDropdownOpen(false);
          }}
        >
          <SelectTrigger className="w-full h-auto min-h-[60px] p-4 border-2 border-gray-200 hover:border-teal-300 focus:border-teal-400 rounded-xl bg-white shadow-sm">
            <SelectValue>
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center">
                  {selectedDurationIndex === 1 && (
                    <Star className="h-4 w-4 text-yellow-500 mr-2" />
                  )}
                  <div className="text-left">
                    <div className="font-semibold text-gray-900">
                      {selectedDuration.label}
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedDuration.valueInDays} days •{" "}
                      {selectedDuration.paymentType}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-teal-600">
                    ₹{(selectedDuration.price || 0).toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">
                    ₹
                    {Math.round(
                      (selectedDuration.price || 0) /
                        (selectedDuration.valueInDays || 1)
                    )}
                    /day
                  </div>
                </div>
              </div>
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="w-full min-w-[400px] max-h-60 bg-white border border-gray-200 rounded-xl shadow-lg">
            {plan.durations.map((duration, index) => (
              <SelectItem
                key={duration.id}
                value={duration.id}
                className="p-4 hover:bg-teal-50 focus:bg-teal-50 cursor-pointer"
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    {index === 1 && (
                      <Star className="h-4 w-4 text-yellow-500 mr-2" />
                    )}
                    <div>
                      <div className="font-semibold text-gray-900">
                        {duration.label}
                      </div>
                      <div className="text-xs text-gray-500">
                        {duration.valueInDays} days • {duration.paymentType}
                      </div>
                    </div>
                  </div>
                  <div className="text-right ml-4">
                    <div className="font-bold text-teal-600">
                      ₹{(duration.price || 0).toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      ₹
                      {Math.round(
                        (duration.price || 0) / (duration.valueInDays || 1)
                      )}
                      /day
                    </div>
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Price Display */}
      <div className="text-center mb-8 p-6 bg-gradient-to-r from-teal-50 to-emerald-50 rounded-2xl border border-teal-200/50">
        <div className="text-4xl font-bold bg-gradient-to-r from-teal-600 to-emerald-600 bg-clip-text text-transparent theme-primary-font">
          ₹{(selectedDuration.price || 0).toLocaleString()}
        </div>
        <div className="text-sm text-gray-600 mt-2">
          {selectedDuration.label} •{" "}
          {selectedDuration.paymentType || "one-time"}
        </div>
        <div className="text-xs text-teal-600 font-medium mt-2 bg-teal-100 px-3 py-1 rounded-full inline-block">
          Save ₹{Math.round((selectedDuration.price || 0) * 0.2)} vs monthly
        </div>
      </div>

      {/* Features List with See More */}
      <div className="mb-8">
        <div className="flex items-center justify-center mb-6">
          <Zap className="h-5 w-5 text-teal-600 mr-2" />
          <span className="text-sm font-semibold text-gray-700">
            What&apos;s Included:
          </span>
        </div>

        <div className="space-y-3">
          {visibleFeatures.map((feature, index) => (
            <div
              key={feature.id}
              className="flex items-start gap-3 p-4 rounded-xl bg-gradient-to-r from-gray-50 to-teal-50/30 hover:from-teal-50 hover:to-teal-100/50 transition-all duration-300 border border-gray-100 hover:border-teal-200"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CheckCircle2 className="text-teal-500 h-5 w-5 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <div className="font-semibold text-gray-900 text-sm">
                  {feature.name}
                </div>
                <div className="text-gray-600 text-xs leading-relaxed mt-1">
                  {feature.description}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* See More/Less Button */}
        {selectedDuration.features && selectedDuration.features.length > 4 && (
          <button
            onClick={() => setShowAllFeatures(!showAllFeatures)}
            className="w-full mt-4 flex items-center justify-center gap-2 py-3 px-4 rounded-xl border border-teal-200 bg-white hover:bg-teal-50 transition-all duration-200 text-teal-600 font-medium text-sm"
          >
            {showAllFeatures ? (
              <>
                <EyeOff className="h-4 w-4" />
                Show Less
              </>
            ) : (
              <>
                <Eye className="h-4 w-4" />
                See More ({selectedDuration.features.length - 4} more)
              </>
            )}
          </button>
        )}
      </div>

      {/* Select Button */}
      <Button
        className="btn-gradient w-full group h-12 text-base font-semibold"
        onClick={() =>
          onSelectPlan(
            plan.id,
            selectedDuration.id,
            selectedDuration.price || 0
          )
        }
      >
        <span className="flex items-center justify-center gap-2">
          Choose {plan.name}
          <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
        </span>
      </Button>
    </div>
  );
};

export default function HomePage() {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [user, setUser] = useState(null);
  const router = useRouter();

  useEffect(() => {
    // Add a small delay to ensure localStorage is available
    const checkAuth = () => {
      const auth = authStorage.getAuth();

      if (!auth || !authStorage.isAuthenticated()) {
        setLoading(false); // Stop loading before redirect
        router.push("/login");
        return;
      }

      setUser(auth);

      // Always fetch plans on mount
      fetchPlans(auth.accessToken);
    };

    // Check auth immediately and also after a small delay for SSR
    checkAuth();
    const timeoutId = setTimeout(checkAuth, 100);

    return () => clearTimeout(timeoutId);
  }, [router]); // Add router as dependency

  const fetchPlans = async (token) => {
    try {
      setLoading(true);
      const plansData = await apiClient.getPlans(token);
      setPlans(plansData);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch plans"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPlan = (planId, durationId, price) => {
    router.push(
      `/order-summary?planId=${planId}&durationId=${durationId}&price=${price}`
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-emerald-50">
        <Header user={user} />
        <div className="flex justify-center items-center h-[80vh]">
          <div className="text-center">
            <LoaderCircle className="animate-spin h-16 w-16 text-teal-600 mx-auto mb-4" />
            <p className="text-gray-600 font-medium theme-secondary-font">
              {user
                ? "Loading your nutrition plans..."
                : "Checking authentication..."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If no user and not loading, show login prompt
  if (!user && !loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-emerald-50">
        <Header user={user} />
        <div className="flex flex-col justify-center items-center h-[80vh] gap-6">
          <div className="text-center max-w-md">
            <div className="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-teal-600 text-2xl">🔐</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2 theme-primary-font">
              Please Log In
            </h2>
            <p className="text-gray-600 mb-6 theme-secondary-font">
              You need to log in to view your nutrition plans.
            </p>
            <Button
              onClick={() => router.push("/login")}
              className="btn-gradient"
            >
              Go to Login
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-emerald-50">
        <Header user={user} />
        <div className="flex flex-col justify-center items-center h-[80vh] gap-6">
          <div className="text-center max-w-md">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-600 text-2xl">⚠️</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2 theme-primary-font">
              Oops! Something went wrong
            </h2>
            <p className="text-red-600 mb-6 theme-secondary-font">{error}</p>
            <Button
              onClick={() => fetchPlans(user?.accessToken)}
              className="btn-gradient"
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-emerald-50">
      <Header user={user} />

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 to-emerald-500/10"></div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center max-w-4xl mx-auto">
            <div className="animate-slide-up">
              <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-teal-600 via-teal-700 to-emerald-600 bg-clip-text text-transparent mb-6 theme-primary-font">
                Fuel Your Soul,
                <span className="block">Transform Your Body</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-8 leading-relaxed theme-secondary-font">
                Discover personalized nutrition and fitness plans that nourish
                your body and elevate your spirit. Start your holistic wellness
                journey today.
              </p>
            </div>

            {/* Stats */}
            <div
              className="grid grid-cols-3 gap-8 max-w-2xl mx-auto mb-12 animate-slide-up"
              style={{ animationDelay: "0.2s" }}
            >
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Users className="h-6 w-6 text-teal-600 mr-2" />
                  <div className="text-3xl font-bold text-teal-600 theme-primary-font">
                    5K+
                  </div>
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  Transformed Lives
                </div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Target className="h-6 w-6 text-teal-600 mr-2" />
                  <div className="text-3xl font-bold text-teal-600 theme-primary-font">
                    98%
                  </div>
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  Goal Achievement
                </div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Sparkles className="h-6 w-6 text-teal-600 mr-2" />
                  <div className="text-3xl font-bold text-teal-600 theme-primary-font">
                    24/7
                  </div>
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  Expert Support
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gradient-to-b from-white to-teal-50/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6 theme-primary-font">
              Nourish Your Journey with{" "}
              <span className="bg-gradient-to-r from-teal-600 to-emerald-600 bg-clip-text text-transparent">
                Perfect Nutrition Plans
              </span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed theme-secondary-font">
              Unlock your body&apos;s potential with our scientifically-designed
              nutrition and wellness programs. Each plan is crafted to fuel your
              transformation and support your unique health goals.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 justify-center items-stretch gap-4 mx-auto max-w-7xl">
            {plans.map((plan, index) => (
              <div
                key={plan.id}
                className="flex-1 max-w-md mx-auto"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <PricingCard
                  plan={plan}
                  onSelectPlan={handleSelectPlan}
                  isPopular={index === 1} // Middle plan is popular
                />
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
